<!--
 * @Description: 个人资料页面 - 简洁版用户信息编辑
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 简洁表单布局，仅包含头像、昵称、性别、生日
-->
<template>
  <view class="profile-container">
    <!-- 头像设置 -->
    <view class="avatar-section">
      <view class="section-title">头像设置</view>
      <view class="avatar-wrapper" @click="chooseAvatar">
        <image
          class="avatar-image"
          :src="userInfo.avatar || defaultAvatar"
          mode="aspectFill"
        />
        <view class="avatar-mask">
          <text class="camera-icon">📷</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>

      <!-- 昵称 -->
      <view class="info-item">
        <view class="item-label">昵称</view>
        <input
          class="item-input"
          v-model="userInfo.nickname"
          placeholder="请输入昵称"
          maxlength="20"
        />
      </view>

      <!-- 性别 -->
      <view class="info-item">
        <view class="item-label">性别</view>
        <view class="gender-options">
          <view
            class="gender-item"
            :class="{ selected: userInfo.gender === 'male' }"
            @click="selectGender('male')"
          >
            <text class="gender-emoji">👨</text>
            <text class="gender-label">男</text>
          </view>
          <view
            class="gender-item"
            :class="{ selected: userInfo.gender === 'female' }"
            @click="selectGender('female')"
          >
            <text class="gender-emoji">👩</text>
            <text class="gender-label">女</text>
          </view>
          <view
            class="gender-item"
            :class="{ selected: userInfo.gender === 'other' }"
            @click="selectGender('other')"
          >
            <text class="gender-emoji">🤖</text>
            <text class="gender-label">其他</text>
          </view>
        </view>
      </view>

      <!-- 生日 -->
      <view class="info-item">
        <view class="item-label">生日</view>
        <view class="date-selector" @click="showDatePicker">
          <text class="date-value">{{ userInfo.birthday || '请选择生日' }}</text>
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 固定底部保存按钮 -->
    <view class="fixed-bottom">
      <button class="save-button" @click="saveProfile" :disabled="saving">
        {{ saving ? '保存中...' : '保存' }}
      </button>
    </view>

    <!-- 日期选择器 -->
    <u-popup v-model="showDatePickerPopup" mode="bottom" border-radius="20">
      <view class="date-picker-popup">
        <view class="picker-header">
          <view class="picker-cancel" @click="cancelDatePicker">取消</view>
          <view class="picker-title">选择生日</view>
          <view class="picker-confirm" @click="confirmDatePicker">确定</view>
        </view>
        <picker-view
          class="picker-view"
          :value="pickerValue"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view v-for="(year, index) in years" :key="index" class="picker-item">
              {{ year }}年
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(month, index) in months" :key="index" class="picker-item">
              {{ month }}月
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(day, index) in days" :key="index" class="picker-item">
              {{ day }}日
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      saving: false,
      defaultAvatar: '/static/placeholder.png',
      showDatePickerPopup: false,
      pickerValue: [0, 0, 0],
      years: [],
      months: [],
      days: [],
      userInfo: {
        nickname: '',
        avatar: '',
        gender: '',
        birthday: ''
      }
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.loadUserInfo()
      this.initDatePicker()
    },

    /**
     * 加载用户信息
     */
    loadUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = { ...this.userInfo, ...userInfo }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 初始化日期选择器
     */
    initDatePicker() {
      const currentYear = new Date().getFullYear()

      // 生成年份数组（1950-当前年份）
      this.years = []
      for (let i = 1950; i <= currentYear; i++) {
        this.years.push(i)
      }

      // 生成月份数组
      this.months = []
      for (let i = 1; i <= 12; i++) {
        this.months.push(i)
      }

      // 生成日期数组
      this.updateDays()
    },

    /**
     * 更新日期数组
     */
    updateDays() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const daysInMonth = new Date(year, month, 0).getDate()

      this.days = []
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i)
      }
    },

    /**
     * 选择头像
     */
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('选择头像失败:', error)
        }
      })
    },

    /**
     * 选择性别
     */
    selectGender(gender) {
      this.userInfo.gender = gender
    },

    /**
     * 显示日期选择器
     */
    showDatePicker() {
      this.showDatePickerPopup = true
    },

    /**
     * 日期选择器值变化
     */
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.updateDays()
    },

    /**
     * 取消日期选择
     */
    cancelDatePicker() {
      this.showDatePickerPopup = false
    },

    /**
     * 确认日期选择
     */
    confirmDatePicker() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const day = this.days[this.pickerValue[2]]

      this.userInfo.birthday = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
      this.showDatePickerPopup = false
    },

    /**
     * 保存个人资料
     */
    async saveProfile() {
      if (this.saving) return

      // 基本验证
      if (!this.userInfo.nickname.trim()) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      this.saving = true

      try {
        // 保存到本地存储
        uni.setStorageSync('userInfo', this.userInfo)

        // 模拟网络请求延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx; /* 为固定底部按钮留出空间 */
}

.avatar-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 40rpx;
  }

  .avatar-wrapper {
    display: flex;
    justify-content: center;
    position: relative;

    .avatar-image {
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      border: 6rpx solid #f0f0f0;
    }

    .avatar-mask {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .camera-icon {
        font-size: 48rpx;
        color: white;
      }
    }

    &:active .avatar-mask {
      opacity: 1;
    }
  }
}

.info-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 40rpx;
  }
}

.info-item {
  margin-bottom: 40rpx;

  .item-label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
  }

  .item-input {
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    background: #fafafa;
    transition: all 0.3s ease;

    &:focus {
      border-color: #07c160;
      background: white;
    }
  }

  .gender-options {
    display: flex;
    gap: 20rpx;

    .gender-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx 16rpx;
      border: 2rpx solid #e8e8e8;
      border-radius: 12rpx;
      background: #fafafa;
      transition: all 0.3s ease;

      &.selected {
        border-color: #07c160;
        background: #f0f9f4;
      }

      .gender-emoji {
        font-size: 40rpx;
        margin-bottom: 8rpx;
      }

      .gender-label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .date-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 24rpx;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
    background: #fafafa;
    transition: all 0.3s ease;

    &:active {
      background: white;
      border-color: #07c160;
    }

    .date-value {
      font-size: 28rpx;
      color: #333;
    }

    .arrow-right {
      font-size: 32rpx;
      color: #999;
      font-weight: bold;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .save-button {
    width: 100%;
    height: 88rpx;
    background: #07c160;
    color: white;
    border: none;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &:disabled {
      background: #ccc;
      opacity: 0.6;
    }

    &:not(:disabled):active {
      background: #06ad56;
      transform: scale(0.98);
    }
  }
}

.date-picker-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .picker-cancel, .picker-confirm {
      font-size: 28rpx;
      color: #07c160;
      font-weight: 500;
    }

    .picker-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1a1a1a;
    }
  }

  .picker-view {
    height: 400rpx;

    .picker-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
