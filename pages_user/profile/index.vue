<!--
 * @Description: 个人资料页面 - 编辑用户信息
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 表单布局，支持头像上传、昵称编辑等
-->
<template>
  <view class="user-page-container">
    <!-- 头像编辑区域 -->
    <view class="user-card">
      <view class="card-title">头像设置</view>
      <view class="avatar-edit-section">
        <view class="avatar-container" @click="chooseAvatar">
          <image
            class="user-avatar"
            :src="userInfo.avatar || defaultAvatar"
            mode="aspectFill"
          />
          <view class="avatar-overlay">
            <text class="camera-icon">📷</text>
            <text class="edit-text">点击更换</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息编辑 -->
    <view class="user-card">
      <view class="card-title">基本信息</view>
      <view class="form-section">
        <view class="form-item">
          <view class="form-label">昵称</view>
          <input
            class="form-input"
            v-model="userInfo.nickname"
            placeholder="请输入昵称"
            maxlength="20"
          />
        </view>

        <view class="form-item">
          <view class="form-label">个性签名</view>
          <textarea
            class="form-textarea"
            v-model="userInfo.signature"
            placeholder="写点什么介绍一下自己吧"
            maxlength="100"
            :show-confirm-bar="false"
          />
          <view class="char-count">{{ userInfo.signature.length }}/100</view>
        </view>

        <view class="form-item">
          <view class="form-label">性别</view>
          <view class="gender-selector">
            <view
              class="gender-option"
              :class="{ active: userInfo.gender === 'male' }"
              @click="selectGender('male')"
            >
              <text class="gender-icon">👨</text>
              <text class="gender-text">男</text>
            </view>
            <view
              class="gender-option"
              :class="{ active: userInfo.gender === 'female' }"
              @click="selectGender('female')"
            >
              <text class="gender-icon">👩</text>
              <text class="gender-text">女</text>
            </view>
            <view
              class="gender-option"
              :class="{ active: userInfo.gender === 'other' }"
              @click="selectGender('other')"
            >
              <text class="gender-icon">🤖</text>
              <text class="gender-text">其他</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">生日</view>
          <view class="date-picker" @click="showDatePicker">
            <text class="date-text">{{ userInfo.birthday || '请选择生日' }}</text>
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="user-card">
      <view class="card-title">联系方式</view>
      <view class="form-section">
        <view class="form-item">
          <view class="form-label">手机号</view>
          <input
            class="form-input"
            v-model="userInfo.phone"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
          />
        </view>

        <view class="form-item">
          <view class="form-label">邮箱</view>
          <input
            class="form-input"
            v-model="userInfo.email"
            placeholder="请输入邮箱地址"
            type="email"
          />
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <button class="save-btn" @click="saveProfile" :disabled="saving">
        <text v-if="saving">保存中...</text>
        <text v-else>保存修改</text>
      </button>
    </view>

    <!-- 日期选择器 -->
    <u-popup v-model="showDatePickerPopup" mode="bottom" border-radius="20">
      <view class="date-picker-popup">
        <view class="picker-header">
          <view class="picker-cancel" @click="cancelDatePicker">取消</view>
          <view class="picker-title">选择生日</view>
          <view class="picker-confirm" @click="confirmDatePicker">确定</view>
        </view>
        <picker-view
          class="picker-view"
          :value="pickerValue"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view v-for="(year, index) in years" :key="index" class="picker-item">
              {{ year }}年
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(month, index) in months" :key="index" class="picker-item">
              {{ month }}月
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(day, index) in days" :key="index" class="picker-item">
              {{ day }}日
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      saving: false,
      defaultAvatar: '/static/placeholder.png',
      showDatePickerPopup: false,
      pickerValue: [0, 0, 0],
      years: [],
      months: [],
      days: [],
      userInfo: {
        nickname: '',
        signature: '',
        avatar: '',
        gender: '',
        birthday: '',
        phone: '',
        email: ''
      }
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.loadUserInfo()
      this.initDatePicker()
    },

    /**
     * 加载用户信息
     */
    loadUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = { ...this.userInfo, ...userInfo }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 初始化日期选择器
     */
    initDatePicker() {
      const currentYear = new Date().getFullYear()

      // 生成年份数组（1950-当前年份）
      this.years = []
      for (let i = 1950; i <= currentYear; i++) {
        this.years.push(i)
      }

      // 生成月份数组
      this.months = []
      for (let i = 1; i <= 12; i++) {
        this.months.push(i)
      }

      // 生成日期数组
      this.updateDays()
    },

    /**
     * 更新日期数组
     */
    updateDays() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const daysInMonth = new Date(year, month, 0).getDate()

      this.days = []
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i)
      }
    },

    /**
     * 选择头像
     */
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('选择头像失败:', error)
        }
      })
    },

    /**
     * 选择性别
     */
    selectGender(gender) {
      this.userInfo.gender = gender
    },

    /**
     * 显示日期选择器
     */
    showDatePicker() {
      this.showDatePickerPopup = true
    },

    /**
     * 日期选择器值变化
     */
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.updateDays()
    },

    /**
     * 取消日期选择
     */
    cancelDatePicker() {
      this.showDatePickerPopup = false
    },

    /**
     * 确认日期选择
     */
    confirmDatePicker() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const day = this.days[this.pickerValue[2]]

      this.userInfo.birthday = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
      this.showDatePickerPopup = false
    },

    /**
     * 保存个人资料
     */
    async saveProfile() {
      if (this.saving) return

      // 基本验证
      if (!this.userInfo.nickname.trim()) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      this.saving = true

      try {
        // 保存到本地存储
        uni.setStorageSync('userInfo', this.userInfo)

        // 模拟网络请求延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.card-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: $spacing-lg;
}

.avatar-edit-section {
  display: flex;
  justify-content: center;
  padding: $spacing-lg 0;

  .avatar-container {
    position: relative;
    width: 160rpx;
    height: 160rpx;

    .user-avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 4rpx solid $divider-color;
    }

    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity $transition-normal;

      .camera-icon {
        font-size: $font-size-xl;
        margin-bottom: $spacing-xs;
      }

      .edit-text {
        font-size: $font-size-xs;
        color: $white;
      }
    }

    &:active .avatar-overlay {
      opacity: 1;
    }
  }
}

.form-section {
  .form-item {
    margin-bottom: $spacing-lg;

    .form-label {
      font-size: $font-size-md;
      color: $text-primary;
      margin-bottom: $spacing-sm;
      font-weight: 500;
    }

    .form-input, .form-textarea {
      width: 100%;
      padding: $spacing-md;
      border: 1rpx solid $border-color;
      border-radius: $border-radius-md;
      font-size: $font-size-md;
      color: $text-primary;
      background: $white;

      &:focus {
        border-color: $primary-color;
      }
    }

    .form-textarea {
      min-height: 120rpx;
      resize: none;
    }

    .char-count {
      text-align: right;
      font-size: $font-size-xs;
      color: $text-light;
      margin-top: $spacing-xs;
    }

    .gender-selector {
      display: flex;
      gap: $spacing-md;

      .gender-option {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: $spacing-lg $spacing-md;
        border: 1rpx solid $border-color;
        border-radius: $border-radius-md;
        transition: all $transition-normal;

        &.active {
          border-color: $primary-color;
          background: $primary-light;
        }

        .gender-icon {
          font-size: $font-size-xl;
          margin-bottom: $spacing-xs;
        }

        .gender-text {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }

    .date-picker {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-md;
      border: 1rpx solid $border-color;
      border-radius: $border-radius-md;
      background: $white;

      .date-text {
        font-size: $font-size-md;
        color: $text-primary;
      }

      .arrow-icon {
        font-size: $font-size-lg;
        color: $text-light;
      }
    }
  }
}

.save-section {
  margin-top: $spacing-xl;

  .save-btn {
    width: 100%;
    background: $primary-color;
    color: $white;
    border: none;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    font-size: $font-size-md;
    font-weight: 500;
    transition: all $transition-normal;

    &:disabled {
      background: $text-light;
      opacity: 0.6;
    }

    &:not(:disabled):active {
      background: $primary-dark;
      transform: scale(0.98);
    }
  }
}

.date-picker-popup {
  background: $white;
  border-radius: 20rpx 20rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .picker-cancel, .picker-confirm {
      font-size: $font-size-md;
      color: $primary-color;
    }

    .picker-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }
  }

  .picker-view {
    height: 400rpx;

    .picker-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80rpx;
      font-size: $font-size-md;
      color: $text-primary;
    }
  }
}
</style>
