<!--
 * @Description: 个人中心主页 - 仿千库设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 用户信息头部 + 功能图标网格 + 功能列表
-->
<template>
  <view class="user-center">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <view class="avatar-wrapper" @click="goToProfile">
          <u-avatar
            :src="userInfo.avatar"
            size="60"
            color="#ffffff"
            text="AI"
            bg-color="#007aff"
            :random-bg-color="false"
          ></u-avatar>
        </view>
        <view class="user-details" @click="goToProfile">
          <view class="username">{{ userInfo.nickname || '可爱的千库' }}</view>
          <view class="level-container">
            <view class="user-level">Lv 8</view>
            <view class="vip-badge" v-if="userInfo.isVip">VIP</view>
          </view>
        </view>
        <view class="check-in" @click="handleCheckIn">
          <text class="check-icon">✓</text>
          <text class="check-text">已签到</text>
        </view>
      </view>

      <!-- 用户统计 -->
      <view class="user-stats">
        <view class="stat-item">
          <view class="stat-number">45</view>
          <view class="stat-label">动态</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">170</view>
          <view class="stat-label">关注</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">211</view>
          <view class="stat-label">粉丝</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">356</view>
          <view class="stat-label">好友</view>
        </view>
      </view>

      <!-- VIP卡片 -->
      <view class="vip-card" @click="handleVip">
        <view class="vip-content">
          <view class="vip-title">开启千库VIP</view>
          <view class="vip-desc">六大权限一尽享！品质首好年</view>
        </view>
        <view class="vip-btn">开通</view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="function-list">
      <view class="function-row" @click="goToTimer">
        <view class="row-icon">⏰</view>
        <view class="row-title">定时关团</view>
        <view class="row-arrow">›</view>
      </view>
      <view class="function-row" @click="goToAudio">
        <view class="row-icon">🔊</view>
        <view class="row-title">智能音效</view>
        <view class="row-arrow">›</view>
      </view>
      <view class="function-row" @click="goToTools">
        <view class="row-icon">🧰</view>
        <view class="row-title">音乐工具</view>
        <view class="row-arrow">›</view>
      </view>
      <view class="function-row" @click="goToAbout">
        <view class="row-icon">ℹ️</view>
        <view class="row-title">关于我们</view>
        <view class="row-arrow">›</view>
      </view>
      <view class="function-row" @click="goToSettings">
        <view class="row-icon">⚙️</view>
        <view class="row-title">设置</view>
        <view class="row-arrow">›</view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <view class="logout-btn" @click="handleLogout">
        <text>退出登录</text>
      </view>
    </view>

    <!-- 悬浮按钮 -->
    <view class="float-btn" @click="handleFloatAction">
      <text class="plus-icon">+</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserIndex',
  data() {
    return {
      // 用户信息
      userInfo: {
        id: '',
        nickname: '可爱的千库',
        avatar: '',
        level: 8,
        isVip: true // VIP状态
      }
    }
  },

  onLoad() {
    this.loadUserData()
  },

  methods: {
    /**
     * 加载用户数据
     */
    loadUserData() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = {
            ...this.userInfo,
            ...userInfo
          }
        }
      } catch (error) {
        console.error('加载用户数据失败:', error)
      }
    },

    /**
     * 跳转到个人资料
     */
    goToProfile() {
      uni.navigateTo({
        url: '/pages_user/profile/index'
      })
    },

    /**
     * 处理签到
     */
    handleCheckIn() {
      uni.showToast({
        title: '今日已签到',
        icon: 'success'
      })
    },

    /**
     * 处理VIP
     */
    handleVip() {
      uni.navigateTo({
        url: '/pages_user/vip/vip'
      })
    },

    /**
     * 跳转到消息
     */
    goToMessages() {
      uni.navigateTo({
        url: '/pages_user/messages/messages'
      })
    },

    /**
     * 跳转到皮肤中心
     */
    goToSkin() {
      uni.navigateTo({
        url: '/pages_user/skin/skin'
      })
    },

    /**
     * 跳转到会员中心
     */
    goToVip() {
      uni.navigateTo({
        url: '/pages_user/vip/vip'
      })
    },

    /**
     * 跳转到私人网盘
     */
    goToCloud() {
      uni.navigateTo({
        url: '/pages_user/cloud/cloud'
      })
    },

    /**
     * 跳转到定时关团
     */
    goToTimer() {
      uni.navigateTo({
        url: '/pages_user/timer/timer'
      })
    },

    /**
     * 跳转到智能音效
     */
    goToAudio() {
      uni.navigateTo({
        url: '/pages_user/audio/audio'
      })
    },

    /**
     * 跳转到音乐工具
     */
    goToTools() {
      uni.navigateTo({
        url: '/pages_user/tools/tools'
      })
    },

    /**
     * 跳转到关于我们
     */
    goToAbout() {
      uni.navigateTo({
        url: '/pages_user/about/index'
      })
    },

    /**
     * 跳转到设置
     */
    goToSettings() {
      uni.navigateTo({
        url: '/pages_user/settings/index'
      })
    },

    /**
     * 悬浮按钮操作
     */
    handleFloatAction() {
      uni.showActionSheet({
        itemList: ['新建任务', '添加记忆', '记账', '录音'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.navigateTo({ url: '/pages/task/add' })
              break
            case 1:
              uni.navigateTo({ url: '/pages/memory/add' })
              break
            case 2:
              uni.navigateTo({ url: '/pages/accounting/add' })
              break
            case 3:
              uni.navigateTo({ url: '/pages/voice/voice' })
              break
          }
        }
      })
    },

    /**
     * 处理退出登录
     */
    handleLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户数据
            uni.removeStorageSync('assistantUniToken')
            uni.removeStorageSync('assistantUniUserInfo')

            // 跳转到登录页
            uni.reLaunch({
              url: '/pages_chat/login/index'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-center {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 用户信息头部 */
.user-header {
  background: #fff;
  padding: 20rpx 30rpx 30rpx;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-wrapper {
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.level-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-level {
  background: #007aff;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.vip-badge {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.check-in {
  display: flex;
  align-items: center;
  background: #e8f4fd;
  color: #007aff;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}

.check-icon {
  margin-right: 8rpx;
}

/* 用户统计 */
.user-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* VIP卡片 */
.vip-card {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.vip-content {
  flex: 1;
}

.vip-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.vip-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.vip-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 16rpx 32rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 功能图标区域 */
.function-grid {
  background: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
}

.function-item {
  text-align: center;
  flex: 1;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin: 0 auto 16rpx;
}

.message-icon {
  background: #e8f4fd;
}

.skin-icon {
  background: #fff2e8;
}

.vip-icon {
  background: #fff0f0;
}

.cloud-icon {
  background: #f0f9ff;
}

.function-label {
  font-size: 26rpx;
  color: #333;
}

/* 功能列表 */
.function-list {
  background: #fff;
}

.function-row {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.function-row:last-child {
  border-bottom: none;
}

.row-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}

.row-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.row-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 悬浮按钮 */
.float-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007aff;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  z-index: 999;
}

.plus-icon {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 20rpx;
  padding: 20rpx 30rpx 40rpx;
}

.logout-btn {
  background: #fff;
  color: #666;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.logout-btn:active {
  background: #f5f5f5;
  transform: scale(0.98);
}
</style>
