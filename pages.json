{
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue",
		"nv": "@/uni_modules/pyh-nv/components/pyh-nv/pyh-nv.vue"
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "小助理",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/record-test/index",
			"style": {
				"navigationBarTitleText": "录音测试",
				"enablePullDownRefresh": false
			}
		}
	],
	"subPackages": [
		{
			// task 指定任务
			"root": "pages_task",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "待办任务",
						"enablePullDownRefresh": true,
						"onReachBottomDistance": 50,
						"backgroundTextStyle": "dark",
						"app-plus": {
							"titleNView": {
								"buttons": [
									{
									"type": "none",
									"width": "35px",
									"fontSrc": "",
									"text": "⋯",
									"fontSize": "24px",
									"color":"#333"
									}
								]
							}
						}
					}
				},
				{
					"path": "index/list",
					"style": {
						"navigationBarTitleText": "待办列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "detail/index",
					"style": {
						"navigationBarTitleText": "待办详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "detail/save",
					"style": {
						"navigationBarTitleText": "保存待办",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "category-manage/index",
					"style": {
						"navigationBarTitleText": "分类管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tag-manage/index",
					"style": {
						"navigationBarTitleText": "标签管理",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			// memory 记忆
			"root": "pages_memory",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "记忆",
						"enablePullDownRefresh": false,
						"app-plus": {
							"titleNView": {
								"buttons": [
									{
									"type": "none",
									"width": "35px",
									"fontSrc": "",
									"text": "⋯",
									"fontSize": "24px",
									"color":"#333"
									}
								]
							}
						}
					}
				},
				{
					"path": "list/index",
					"style": {
						"navigationBarTitleText": "记忆列表",
						"enablePullDownRefresh": true,
						"onReachBottomDistance": 50,
						"backgroundTextStyle": "dark"
					}
				},
				{
					"path": "detail/index",
					"style": {
						"navigationBarTitleText": "记忆详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "group-manage/index",
					"style": {
						"navigationBarTitleText": "分组管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tag-manage/index",
					"style": {
						"navigationBarTitleText": "标签管理",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			// accounting 记账系统
			"root": "pages_accounting",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "记账",
						"enablePullDownRefresh": true,
						"onReachBottomDistance": 50,
						"backgroundTextStyle": "dark"
					}
				},
				{
					"path": "list/index",
					"style": {
						"navigationBarTitleText": "记账列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "add/index",
					"style": {
						"navigationBarTitleText": "新建记账",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "add/simple",
					"style": {
						"navigationBarTitleText": "新建记账",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "statistics/index",
					"style": {
						"navigationBarTitleText": "统计分析",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tag-manage/index",
					"style": {
						"navigationBarTitleText": "标签管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "category-manage/index",
					"style": {
						"navigationBarTitleText": "分类管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record-detail/index",
					"style": {
						"navigationBarTitleText": "记录详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tagSelect/index",
					"style": {
						"navigationBarTitleText": "选择标签",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			// chat 聊天
			"root": "pages_chat",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "对话",
						"enablePullDownRefresh": false,
						"app-plus": {
							"titleNView": {
								"buttons": [
									{
									"type": "none",
									"width": "35px",
									"fontSrc": "",
									"text": "⋯",
									"fontSize": "24px",
									"color":"#333"
									}
								]
							}
						}
					}
				},
				{
					"path": "login/index",
					"style": {
						"navigationBarTitleText": "登录",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			// wiki 知识库
			"root": "pages_wiki",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "知识库",
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			// user 个人中心
			"root": "pages_user",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "个人中心",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "profile/index",
					"style": {
						"navigationBarTitleText": "个人资料",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "settings/index",
					"style": {
						"navigationBarTitleText": "设置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "about/index",
					"style": {
						"navigationBarTitleText": "关于我们",
						"enablePullDownRefresh": false
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarShadow": {
			"colorType": "grey"
		},
		"navigationBarTitleText": "小助理",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#f8f9fa",
		"backgroundColorTop": "#f8f9fa"
	}
}
